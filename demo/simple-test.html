<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple CoinFlipper Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        canvas {
            border: 2px solid #333;
            border-radius: 8px;
            background: #222;
            display: block;
            margin: 20px auto;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            text-align: center;
            font-size: 18px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🪙 Simple CoinFlipper Test</h1>
    
    <div class="status" id="status">Loading...</div>
    
    <canvas id="coinCanvas" width="400" height="400"></canvas>
    
    <div class="controls">
        <button onclick="startIdle()">Start Idle</button>
        <button onclick="stopIdle()">Stop Idle</button>
        <button onclick="flipCoin()">Flip Coin</button>
        <button onclick="testHeads()">Test Heads</button>
        <button onclick="testTails()">Test Tails</button>
        <button onclick="resetCoin()">Reset Coin</button>
        <button onclick="pulseEffect()">Pulse Effect</button>
    </div>

    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    
    <!-- CoinFlipper Module -->
    <script src="../src/coin-flipper.js"></script>
    
    <script>
        let coinFlipper = null;
        const statusEl = document.getElementById('status');
        
        function updateStatus(message) {
            statusEl.textContent = message;
            console.log(message);
        }
        
        async function init() {
            try {
                updateStatus('Initializing CoinFlipper...');
                
                // Check if Three.js is loaded
                if (typeof THREE === 'undefined') {
                    throw new Error('Three.js not loaded');
                }
                updateStatus('Three.js loaded ✓');
                
                // Check if CoinFlipper is available
                if (typeof CoinFlipper === 'undefined') {
                    throw new Error('CoinFlipper class not available');
                }
                updateStatus('CoinFlipper class available ✓');
                
                // Create CoinFlipper instance
                coinFlipper = new CoinFlipper('coinCanvas', {
                    idleSpeed: 0.02,
                    flipDuration: 2000,
                    enableSound: true
                });
                
                updateStatus('CoinFlipper instance created ✓');
                
                // Wait for ready
                await coinFlipper.ready();
                updateStatus('CoinFlipper ready ✓');
                
                // Start idle animation
                await coinFlipper.startIdle();
                updateStatus('Ready! Coin is spinning ✓');
                
            } catch (error) {
                updateStatus(`Error: ${error.message}`);
                console.error('Initialization error:', error);
            }
        }
        
        async function startIdle() {
            if (!coinFlipper) return;
            try {
                await coinFlipper.startIdle();
                updateStatus('Idle animation started');
            } catch (error) {
                updateStatus(`Error starting idle: ${error.message}`);
            }
        }
        
        async function stopIdle() {
            if (!coinFlipper) return;
            try {
                await coinFlipper.stopIdle();
                updateStatus('Idle animation stopped');
            } catch (error) {
                updateStatus(`Error stopping idle: ${error.message}`);
            }
        }
        
        async function flipCoin() {
            if (!coinFlipper) return;
            try {
                updateStatus('Flipping coin...');
                await coinFlipper.stopIdle();
                const result = await coinFlipper.toss();
                updateStatus(`Result: ${result}`);

                // รอสักครู่แล้วเริ่ม idle อีกครั้งเพื่อรีเซ็ตแสง
                // setTimeout(async () => {
                //     await coinFlipper.startIdle();
                //     updateStatus(`Result: ${result} - Ready for next flip`);
                // }, 1500);
            } catch (error) {
                updateStatus(`Error flipping: ${error.message}`);
            }
        }
        
        async function testHeads() {
            if (!coinFlipper) return;
            try {
                updateStatus('Testing heads...');
                await coinFlipper.stopIdle();
                const result = await coinFlipper.toss('heads');
                updateStatus(`Test result: ${result}`);

                // รอสักครู่แล้วเริ่ม idle อีกครั้งเพื่อรีเซ็ตแสง
                setTimeout(async () => {
                    await coinFlipper.startIdle();
                    updateStatus(`Test result: ${result} - Ready for next test`);
                }, 1500);
            } catch (error) {
                updateStatus(`Error testing heads: ${error.message}`);
            }
        }

        async function testTails() {
            if (!coinFlipper) return;
            try {
                updateStatus('Testing tails...');
                await coinFlipper.stopIdle();
                const result = await coinFlipper.toss('tails');
                updateStatus(`Test result: ${result}`);

                // รอสักครู่แล้วเริ่ม idle อีกครั้งเพื่อรีเซ็ตแสง
                setTimeout(async () => {
                    await coinFlipper.startIdle();
                    updateStatus(`Test result: ${result} - Ready for next test`);
                }, 1500);
            } catch (error) {
                updateStatus(`Error testing tails: ${error.message}`);
            }
        }

        async function resetCoin() {
            if (!coinFlipper) return;
            try {
                await coinFlipper.resetCoin();
                updateStatus('Coin reset to initial state');
            } catch (error) {
                updateStatus(`Error resetting coin: ${error.message}`);
            }
        }

        async function pulseEffect() {
            if (!coinFlipper) return;
            try {
                await coinFlipper.pulseEffect();
                updateStatus('Pulse effect triggered');
            } catch (error) {
                updateStatus(`Error with pulse effect: ${error.message}`);
            }
        }

        // Initialize when page loads
        window.addEventListener('load', init);
    </script>
</body>
</html>
